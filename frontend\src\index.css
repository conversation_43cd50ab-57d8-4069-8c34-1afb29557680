@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;700&display=swap');
@import './styles/variables.css';
@import './styles/modal.css';
@import './styles/mobile.css';

@layer base {
  html {
    @apply font-montserrat;
  }

  /* Dark mode transitions */
  body {
    @apply transition-colors duration-200 ease-in-out;
  }

  /* Dark mode scrollbar */
  .dark ::-webkit-scrollbar {
    width: 12px;
  }

  .dark ::-webkit-scrollbar-track {
    @apply bg-dark-secondary;
  }

  .dark ::-webkit-scrollbar-thumb {
    @apply bg-dark-border rounded-full;
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    @apply bg-dark-hover;
  }
}

@layer components {
  /* Base Layout */
  .app {
    @apply min-h-screen bg-amspm-background dark:bg-dark-primary flex flex-col;
  }
  .container {
    @apply w-full max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8;
  }

  /* Navbar and Sidebar */
  .navbar {
    @apply bg-amspm-secondary dark:bg-dark-secondary text-amspm-text dark:text-dark-text p-4 flex justify-between items-center border-b border-amspm-light-gray dark:border-gray-700 sticky top-0 z-20;
  }
  .navbar-left {
    @apply flex items-center;
  }
  .navbar-brand {
    @apply text-2xl font-bold text-amspm-primary uppercase;
  }
  .navbar-links {
    @apply flex flex-col md:flex-row md:space-x-6 space-y-4 md:space-y-0;
  }
  .navbar-link {
    @apply text-amspm-text uppercase font-medium hover:text-amspm-primary transition duration-200;
  }
  .navbar-link.active {
    @apply text-amspm-primary font-bold;
  }

  /* Sidebar */
  .sidebar {
    @apply fixed top-0 left-0 h-full bg-white dark:bg-dark-secondary shadow-lg z-30 transition-all duration-300 ease-in-out overflow-hidden;
  }
  .sidebar-header {
    @apply flex items-center justify-between h-16 px-4 border-b border-amspm-light-gray dark:border-gray-700;
  }
  .sidebar-content {
    @apply flex flex-col h-[calc(100%-4rem)];
  }
  .sidebar-footer {
    @apply mt-auto border-t border-amspm-light-gray dark:border-gray-700 p-4;
  }
  .sidebar-nav-item {
    @apply flex items-center py-3 px-4 w-full rounded-lg transition-colors duration-200;
  }
  .sidebar-nav-item.active {
    @apply bg-amspm-primary text-amspm-secondary font-medium;
  }
  .sidebar-nav-item:not(.active) {
    @apply text-amspm-text hover:bg-amspm-light-gray dark:text-dark-text dark:hover:bg-gray-700;
  }

  /* Headings */
  h1 {
    @apply text-3xl md:text-4xl font-bold text-amspm-primary dark:text-dark-accent uppercase mb-6;
  }
  h2 {
    @apply text-2xl font-semibold text-amspm-text dark:text-dark-text uppercase mb-4;
  }
  h3 {
    @apply text-xl font-medium text-amspm-text dark:text-dark-text mb-2;
  }

  /* Cards */
  .card {
    @apply bg-white dark:bg-dark-secondary p-6 rounded-lg shadow-sm border border-amspm-light-gray dark:border-dark-border;
  }
  .card-header {
    @apply border-b border-amspm-light-gray dark:border-dark-border pb-3 mb-4;
  }
  .card-content {
    @apply space-y-4 text-amspm-text dark:text-dark-text;
  }

  /* Generic dark mode overrides for all cards and tables */
  .dark div[class*="bg-white"] {
    @apply bg-dark-secondary;
  }

  .dark div[class*="rounded-lg shadow"] {
    @apply border-dark-border;
  }

  .dark th[class*="bg-gray-50"] {
    @apply bg-dark-tertiary text-dark-text-light border-dark-border;
  }

  .dark td, .dark th {
    @apply border-dark-border text-dark-text;
  }

  .dark tr {
    @apply border-dark-border;
  }

  .dark tr:hover {
    @apply bg-dark-hover;
  }

  .dark tbody {
    @apply divide-dark-border;
  }

  .dark h3[class*="text-amspm-primary"] {
    @apply text-dark-accent;
  }

  .dark p[class*="text-gray-600"],
  .dark p[class*="text-gray-500"],
  .dark span[class*="text-gray-500"] {
    @apply text-dark-text-light;
  }

  .dark span[class*="bg-green-100"] {
    @apply bg-green-900 text-green-200;
  }

  .dark span[class*="bg-yellow-100"] {
    @apply bg-yellow-900 text-yellow-200;
  }

  .dark div[class*="border-t border-gray-200"] {
    @apply border-dark-border;
  }

  /* Fix for search boxes and inputs */
  .dark input[class*="bg-white"] {
    @apply bg-dark-input text-dark-text;
  }

  /* Fix for "No results found" messages */
  .dark div[class*="text-center text-gray-500"] {
    @apply text-dark-text-light;
  }

  /* Fix for modals and dialogs */
  .dark div[class*="bg-white p-4"] {
    @apply bg-dark-secondary text-dark-text;
  }

  /* Fix for status badges */
  .dark span[class*="bg-red-100"] {
    @apply bg-red-900 text-red-200;
  }

  .dark span[class*="bg-blue-100"] {
    @apply bg-blue-900 text-blue-200;
  }

  /* Fix for section headers */
  .dark h2[class*="text-amspm-text"] {
    @apply text-dark-text;
  }

  /* Fix for links */
  .dark a[class*="text-amspm-primary"],
  .dark button[class*="text-amspm-primary"],
  .dark span[class*="text-amspm-primary"] {
    @apply text-dark-accent hover:text-dark-accent-hover;
  }

  /* Fix for table backgrounds */
  .dark table {
    @apply bg-dark-secondary;
  }

  /* Fix for pagination */
  .dark .pagination button,
  .dark .pagination span,
  .dark .pagination div {
    @apply text-dark-text border-dark-border;
  }

  .dark .pagination button:hover {
    @apply bg-dark-hover;
  }

  .dark .pagination button.active,
  .dark .pagination button[aria-current="true"] {
    @apply bg-dark-accent text-white border-dark-accent;
  }

  /* Fix for pagination numbers */
  .dark .pagination button[class*="bg-blue-500"],
  .dark .pagination button[class*="bg-amspm-primary"] {
    @apply bg-dark-accent text-white border-dark-accent;
  }

  /* Fix for empty state messages */
  .dark td[class*="text-center"] {
    @apply text-dark-text-light;
  }

  /* Fix for profile page and forms */
  .dark label[class*="text-gray-700"],
  .dark label[class*="text-gray-600"] {
    @apply text-dark-text-light;
  }

  .dark p[class*="text-gray-700"],
  .dark p[class*="text-gray-600"] {
    @apply text-dark-text-light;
  }

  .dark input[type="text"],
  .dark input[type="email"],
  .dark input[type="password"],
  .dark input[type="number"],
  .dark input[type="tel"],
  .dark input[type="url"],
  .dark input[type="date"],
  .dark input[type="datetime-local"],
  .dark textarea,
  .dark select {
    @apply bg-dark-input text-dark-text border-dark-border;
  }

  .dark input:focus,
  .dark textarea:focus,
  .dark select:focus {
    @apply bg-dark-input-focus border-dark-accent;
  }

  /* Fix for profile info */
  .dark div[class*="border-b border-gray-200"] {
    @apply border-dark-border;
  }

  /* Buttons */
  .btn {
    @apply bg-amspm-secondary dark:bg-dark-tertiary text-amspm-text dark:text-dark-text border border-amspm-text dark:border-dark-border-light px-3 sm:px-4 py-2 rounded uppercase text-sm sm:text-base font-medium hover:bg-amspm-primary hover:text-amspm-secondary dark:hover:bg-dark-accent dark:hover:text-white transition duration-200 flex items-center justify-center min-h-[40px] touch-manipulation;
  }
  .btn-secondary {
    @apply bg-amspm-primary dark:bg-dark-accent text-amspm-secondary dark:text-white px-3 sm:px-4 py-2 rounded uppercase text-sm sm:text-base font-medium hover:bg-opacity-90 dark:hover:bg-dark-accent-hover transition duration-200 flex items-center justify-center min-h-[40px] touch-manipulation;
  }
  .btn-danger {
    @apply bg-red-500 dark:bg-dark-error text-white px-3 sm:px-4 py-2 rounded uppercase text-sm sm:text-base font-medium hover:bg-opacity-90 dark:hover:bg-opacity-80 transition duration-200 flex items-center justify-center min-h-[40px] touch-manipulation;
  }
  .btn-outline {
    @apply border border-amspm-primary dark:border-dark-accent text-amspm-primary dark:text-dark-accent px-3 sm:px-4 py-2 rounded uppercase text-sm sm:text-base font-medium hover:bg-amspm-primary hover:text-amspm-secondary dark:hover:bg-dark-accent dark:hover:text-white transition duration-200 flex items-center justify-center min-h-[40px] touch-manipulation;
  }
  .btn-ghost {
    @apply text-amspm-primary dark:text-dark-accent hover:text-amspm-text dark:hover:text-dark-text-light transition duration-200 flex items-center justify-center touch-manipulation;
  }

  /* Disabled button states */
  .btn:disabled, .btn-secondary:disabled, .btn-danger:disabled, .btn-outline:disabled {
    @apply opacity-50 cursor-not-allowed hover:bg-opacity-100;
  }

  /* Tables */
  .table {
    @apply w-full border-collapse;
  }
  .table-header {
    @apply bg-amspm-primary dark:bg-dark-tertiary text-amspm-secondary dark:text-dark-text;
  }
  .table-row {
    @apply border-b border-amspm-light-gray dark:border-dark-border hover:bg-amspm-light-gray dark:hover:bg-dark-hover transition duration-150;
  }
  .table-head {
    @apply p-2 sm:p-3 text-left font-semibold uppercase text-xs sm:text-sm text-amspm-text dark:text-dark-text-light;
  }
  .table-cell {
    @apply p-2 sm:p-3 text-amspm-text dark:text-dark-text text-sm;
  }

  /* Responsive tables */
  .table-responsive {
    @apply w-full overflow-x-auto -mx-4 px-4;
  }

  /* Mobile card view for tables */
  .mobile-card {
    @apply bg-white dark:bg-dark-secondary p-4 rounded-lg shadow-sm border border-amspm-light-gray dark:border-dark-border mb-4;
  }

  .mobile-card-header {
    @apply flex justify-between items-start mb-3;
  }

  .mobile-card-title {
    @apply font-bold text-amspm-primary dark:text-dark-accent text-lg;
  }

  .mobile-card-content {
    @apply space-y-2;
  }

  .mobile-card-row {
    @apply flex justify-between items-center;
  }

  .mobile-card-label {
    @apply text-sm text-gray-600 dark:text-dark-text-light font-medium;
  }

  .mobile-card-value {
    @apply text-amspm-text dark:text-dark-text;
  }

  /* Forms and Inputs */
  .form-group {
    @apply mb-4;
  }
  .form-group label {
    @apply block text-amspm-text dark:text-dark-text-light font-medium mb-1 uppercase text-sm sm:text-base;
  }
  .input {
    @apply w-full p-2 border border-amspm-light-gray dark:border-dark-border rounded focus:outline-none focus:ring-2 focus:ring-amspm-primary dark:focus:ring-dark-accent dark:bg-dark-input dark:text-dark-text transition duration-200 text-sm sm:text-base min-h-[40px] touch-manipulation;
  }
  .input:disabled {
    @apply bg-gray-100 dark:bg-dark-tertiary dark:text-dark-text-light cursor-not-allowed;
  }
  .input:focus {
    @apply dark:bg-dark-input-focus;
  }

  /* Form layouts */
  .form-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4;
  }

  .form-row {
    @apply flex flex-col sm:flex-row gap-4 mb-4;
  }

  /* Form feedback */
  .form-error {
    @apply text-red-600 dark:text-dark-error text-sm mt-1;
  }

  .form-help {
    @apply text-gray-500 dark:text-dark-text-light text-sm mt-1;
  }

  /* Select inputs */
  select {
    @apply appearance-none bg-white dark:bg-dark-input text-amspm-text dark:text-dark-text border border-amspm-light-gray dark:border-dark-border rounded p-2 pr-8 focus:outline-none focus:ring-2 focus:ring-amspm-primary dark:focus:ring-dark-accent text-sm sm:text-base min-h-[40px] touch-manipulation;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23666666'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.5rem center;
    background-size: 1.5em 1.5em;
  }

  select:focus {
    @apply dark:bg-dark-input-focus;
  }

  .dark select {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%23b3b3b3'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  }

  /* FullCalendar */
  .fc {
    @apply bg-white dark:bg-dark-secondary rounded-lg shadow-sm p-4 !important;
  }
  .fc .fc-daygrid-day {
    @apply border-amspm-light-gray dark:border-dark-border !important;
    min-height: 100px !important;
  }
  .fc .fc-col-header-cell {
    @apply bg-amspm-primary dark:bg-dark-tertiary text-amspm-secondary dark:text-dark-text !important;
  }
  .fc .fc-daygrid-day-frame {
    @apply dark:bg-dark-secondary !important;
  }
  .fc .fc-day-today {
    @apply dark:bg-dark-hover !important;
  }
  .fc .fc-daygrid-day-number {
    @apply dark:text-dark-text !important;
  }
  .fc .fc-daygrid-event {
    @apply bg-amspm-primary dark:bg-dark-accent text-amspm-secondary dark:text-white rounded-sm !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding: 2px 4px !important;
    font-size: 0.75rem !important;
    white-space: normal !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-height: 40px !important;
  }
  .fc .fc-daygrid-event-harness {
    margin-top: 1px !important;
    margin-bottom: 1px !important;
    width: 100% !important;
  }
  .fc .fc-button {
    @apply bg-amspm-primary dark:bg-dark-accent text-amspm-secondary dark:text-white hover:bg-opacity-90 dark:hover:bg-dark-accent-hover !important;
  }
  .fc-daygrid-day-events {
    min-height: 20px !important;
  }
  .fc-event-title {
    font-weight: 500 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }
  .completed-event {
    @apply bg-green-500 dark:bg-dark-success !important;
    border-color: #10b981 !important;
  }
  .dark .completed-event {
    border-color: #0d9668 !important;
  }
  .pending-event {
    border-width: 1px !important;
  }

  /* Status Dots */
  .status-dot {
    @apply inline-block w-3 h-3 rounded-full mr-2;
  }
  .status-dot-green {
    @apply bg-green-500 dark:bg-dark-success;
  }
  .status-dot-orange {
    @apply bg-orange-500 dark:bg-dark-warning;
  }
  .status-dot-red {
    @apply bg-red-500 dark:bg-dark-error;
  }
  .status-dot-gray {
    @apply bg-gray-500 dark:bg-gray-600;
  }

  /* Modals */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
    pointer-events: auto !important;
  }
  .modal-content {
    @apply bg-white dark:bg-dark-secondary rounded-lg shadow-sm p-6 w-full max-w-md border border-amspm-light-gray dark:border-dark-border text-amspm-text dark:text-dark-text;
    pointer-events: auto !important;
    position: relative;
    z-index: 60;
  }

  .modal-button {
    pointer-events: auto !important;
    cursor: pointer !important;
    position: relative;
    z-index: 100;
  }

  /* Ensure all modal buttons and close buttons work */
  .modal-content button,
  .modal-overlay button {
    pointer-events: auto !important;
    cursor: pointer !important;
  }

  /* Loading Spinner */
  .spinner {
    @apply w-8 h-8 border-4 border-amspm-light-gray dark:border-dark-border rounded-full border-t-amspm-primary dark:border-t-dark-accent animate-spin mx-auto;
  }

  /* Pagination */
  .pagination {
    @apply flex justify-center items-center space-x-4 mt-6;
  }

  /* Toastify */
  .Toastify__toast--success {
    @apply bg-green-500 text-white rounded-lg !important;
  }
  .Toastify__toast--error {
    @apply bg-red-500 text-white rounded-lg !important;
  }

  /* Document Editor */
  .editor-container {
    @apply bg-white border border-gray-300 rounded-lg;
  }

  .editor-content {
    @apply p-4 outline-none;
  }

  .docx-content p {
    @apply my-2;
  }

  .docx-content h1 {
    @apply text-2xl font-bold mb-4 text-amspm-text uppercase;
  }

  .docx-content h2 {
    @apply text-xl font-semibold mb-3 text-amspm-text;
  }

  .docx-content h3 {
    @apply text-lg font-medium mb-2 text-amspm-text;
  }

  .docx-content ul {
    @apply list-disc pl-5 my-2;
  }

  .docx-content ol {
    @apply list-decimal pl-5 my-2;
  }

  .docx-content table {
    @apply border-collapse border border-gray-300 my-4 w-full;
  }

  .docx-content td, .docx-content th {
    @apply border border-gray-300 p-2;
  }

  .docx-content .caption {
    @apply text-sm text-gray-600 italic text-center my-2;
  }

  .docx-content .checkbox {
    @apply inline-block w-4 h-4 border border-gray-400 rounded align-middle mx-1;
  }

  .docx-content .doc-image {
    @apply max-w-full h-auto my-4 mx-auto block;
  }

  /* Empty paragraphs should still take up space */
  .docx-content p:empty {
    @apply h-5;
  }

  /* Utility Classes */
  .flex-center {
    @apply flex items-center justify-center;
  }
  .space-y-4 > * + * {
    @apply mt-4;
  }

  /* Responsive utilities */
  .responsive-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4;
  }

  .responsive-grid-tight {
    @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3;
  }

  .responsive-flex {
    @apply flex flex-col sm:flex-row flex-wrap gap-4;
  }

  .responsive-container {
    @apply w-full px-3 sm:px-4 md:px-6 py-3 sm:py-4 md:py-6;
  }

  .responsive-card {
    @apply bg-white dark:bg-dark-secondary p-3 sm:p-4 md:p-6 rounded-lg shadow-sm border border-amspm-light-gray dark:border-dark-border;
  }

  .responsive-text {
    @apply text-sm sm:text-base text-amspm-text dark:text-dark-text;
  }

  .responsive-text-light {
    @apply text-sm sm:text-base text-amspm-text-light dark:text-dark-text-light;
  }

  .responsive-heading {
    @apply text-lg sm:text-xl md:text-2xl font-bold text-amspm-primary dark:text-dark-accent;
  }

  .responsive-subheading {
    @apply text-base sm:text-lg font-semibold text-amspm-text dark:text-dark-text;
  }

  .touch-target {
    @apply min-h-[44px] min-w-[44px] touch-manipulation;
  }

  /* Dark mode specific utilities */
  .dark-mode-text {
    @apply text-amspm-text dark:text-dark-text;
  }

  .dark-mode-text-light {
    @apply text-amspm-text-light dark:text-dark-text-light;
  }

  .dark-mode-bg {
    @apply bg-white dark:bg-dark-secondary;
  }

  .dark-mode-border {
    @apply border-amspm-light-gray dark:border-dark-border;
  }

  /* Animations */
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: .5;
    }
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}
