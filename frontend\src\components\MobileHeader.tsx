import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FaBell } from "react-icons/fa";
import { User } from "../types/User";

interface MobileHeaderProps {
  user: User;
  onToggleSidebar: () => void;
  onToggleTheme: () => void;
  theme: string;
}

const MobileHeader: React.FC<MobileHeaderProps> = ({
  user,
  onToggleSidebar,
  onToggleTheme,
  theme,
}) => {
  return (
    <header className="bg-white dark:bg-dark-secondary border-b border-amspm-light-gray dark:border-dark-border px-4 py-3 flex items-center justify-between md:hidden">
      {/* Left side - Menu button */}
      <button
        onClick={onToggleSidebar}
        className="p-2 rounded-lg text-amspm-text dark:text-dark-text hover:bg-amspm-light-gray dark:hover:bg-dark-hover transition-colors"
        aria-label="Open menu"
      >
        <FaBars className="text-lg" />
      </button>

      {/* Center - Logo/Title */}
      <div className="flex items-center space-x-2">
        <img
          src="/amspm-logo.png"
          alt="AMSPM Logo"
          className="h-8 w-auto"
          onError={(e) => {
            e.currentTarget.style.display = "none";
          }}
        />
        <span className="text-lg font-semibold text-amspm-text dark:text-dark-text">
          AMSPM
        </span>
      </div>

      {/* Right side - Theme toggle and notifications */}
      <div className="flex items-center space-x-2">
        {/* Notifications */}
        <button
          className="p-2 rounded-lg text-amspm-text dark:text-dark-text hover:bg-amspm-light-gray dark:hover:bg-dark-hover transition-colors relative"
          aria-label="Notifications"
        >
          <FaBell className="text-lg" />
          {/* Notification badge */}
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            3
          </span>
        </button>

        {/* Theme toggle */}
        <button
          onClick={onToggleTheme}
          className="p-2 rounded-lg text-amspm-text dark:text-dark-text hover:bg-amspm-light-gray dark:hover:bg-dark-hover transition-colors"
          aria-label="Toggle theme"
        >
          {theme === "dark" ? (
            <FaSun className="text-lg text-yellow-500" />
          ) : (
            <FaMoon className="text-lg text-blue-500" />
          )}
        </button>

        {/* User avatar */}
        <div className="w-8 h-8 rounded-full bg-amspm-primary dark:bg-dark-accent flex items-center justify-center text-white text-sm font-semibold">
          {user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
        </div>
      </div>
    </header>
  );
};

export default MobileHeader;
