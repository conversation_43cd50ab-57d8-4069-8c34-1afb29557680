import React from "react";
import { Link, useLocation } from "react-router-dom";
import { FaTimes, FaSignOutAlt } from "react-icons/fa";
import { User } from "../types/User";

interface MobileNavigationProps {
  user: User;
  isOpen: boolean;
  onClose: () => void;
  onLogout: () => void;
}

const MobileNavigation: React.FC<MobileNavigationProps> = ({
  user,
  isOpen,
  onClose,
  onLogout,
}) => {
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const adminMenuItems = [
    { path: "/dashboard", label: "Admin Dashboard", icon: "🏠" },
    { path: "/customers", label: "Customers", icon: "👥" },
    { path: "/events", label: "Events", icon: "📅" },
    { path: "/calendar", label: "Calendar", icon: "📆" },
    { path: "/documents", label: "Documents", icon: "📄" },
    { path: "/quotations", label: "Quotations", icon: "💰" },
    { path: "/time-tracking", label: "Time Tracking", icon: "⏰" },
    { path: "/time-tracking-admin", label: "Time Admin", icon: "⏱️" },
    { path: "/users", label: "Users", icon: "👤" },
  ];

  const userMenuItems = [
    { path: "/user-dashboard", label: "Dashboard", icon: "🏠" },
    { path: "/calendar", label: "Calendar", icon: "📅" },
    { path: "/time-tracking", label: "Time Tracking", icon: "⏰" },
  ];

  const menuItems = user.role === "administrator" ? adminMenuItems : userMenuItems;

  return (
    <div
      className={`fixed inset-y-0 left-0 z-50 w-80 bg-white dark:bg-dark-secondary border-r border-amspm-light-gray dark:border-dark-border transform transition-transform duration-300 ease-in-out ${
        isOpen ? "translate-x-0" : "-translate-x-full"
      } md:hidden`}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-amspm-light-gray dark:border-dark-border">
        <div className="flex items-center space-x-3">
          <img
            src="/amspm-logo.png"
            alt="AMSPM Logo"
            className="h-8 w-auto"
            onError={(e) => {
              e.currentTarget.style.display = "none";
            }}
          />
          <span className="text-lg font-semibold text-amspm-text dark:text-dark-text">
            AMSPM
          </span>
        </div>
        <button
          onClick={onClose}
          className="p-2 rounded-lg text-amspm-text dark:text-dark-text hover:bg-amspm-light-gray dark:hover:bg-dark-hover transition-colors"
          aria-label="Close menu"
        >
          <FaTimes className="text-lg" />
        </button>
      </div>

      {/* User Info */}
      <div className="p-4 border-b border-amspm-light-gray dark:border-dark-border">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 rounded-full bg-amspm-primary dark:bg-dark-accent flex items-center justify-center text-white text-lg font-semibold">
            {user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
          </div>
          <div>
            <div className="font-medium text-amspm-text dark:text-dark-text">
              {user.name || user.email}
            </div>
            <div className="text-sm text-amspm-text-light dark:text-dark-text-light capitalize">
              {user.role}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 overflow-y-auto p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => (
            <li key={item.path}>
              <Link
                to={item.path}
                onClick={onClose}
                className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${
                  isActive(item.path)
                    ? "bg-amspm-primary dark:bg-dark-accent text-white"
                    : "text-amspm-text dark:text-dark-text hover:bg-amspm-light-gray dark:hover:bg-dark-hover"
                }`}
              >
                <span className="text-lg">{item.icon}</span>
                <span className="font-medium">{item.label}</span>
              </Link>
            </li>
          ))}
        </ul>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-amspm-light-gray dark:border-dark-border">
        <button
          onClick={() => {
            onLogout();
            onClose();
          }}
          className="flex items-center space-x-3 w-full px-4 py-3 rounded-lg text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
        >
          <FaSignOutAlt className="text-lg" />
          <span className="font-medium">Logout</span>
        </button>
      </div>
    </div>
  );
};

export default MobileNavigation;
