import React, { useState, useEffect } from "react";
import { useAuth } from "../context/AuthContext";
import UserDashboard from "../pages/UserDashboard";
import AdminDashboard from "../pages/AdminDashboard";
import MobileUserDashboard from "../pages/MobileUserDashboard";
import MobileAdminDashboard from "../pages/MobileAdminDashboard";

const ResponsiveDashboard: React.FC = () => {
  const { user } = useAuth();
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  if (!user) return null;

  // Render appropriate dashboard based on screen size and user role
  if (isMobile) {
    return user.role === "administrator" ? <MobileAdminDashboard /> : <MobileUserDashboard />;
  } else {
    return user.role === "administrator" ? <AdminDashboard /> : <UserDashboard />;
  }
};

export default ResponsiveDashboard;
