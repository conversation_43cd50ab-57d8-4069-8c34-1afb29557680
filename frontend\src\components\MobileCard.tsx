import React from "react";

interface MobileCardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  padding?: "sm" | "md" | "lg";
}

const MobileCard: React.FC<MobileCardProps> = ({
  children,
  className = "",
  onClick,
  padding = "md",
}) => {
  const paddingClasses = {
    sm: "p-3",
    md: "p-4",
    lg: "p-6",
  };

  return (
    <div
      className={`bg-white dark:bg-dark-secondary rounded-lg border border-amspm-light-gray dark:border-dark-border shadow-sm ${paddingClasses[padding]} ${
        onClick ? "cursor-pointer hover:shadow-md transition-shadow" : ""
      } ${className}`}
      onClick={onClick}
    >
      {children}
    </div>
  );
};

export default MobileCard;

// Mobile-specific list item component
interface MobileListItemProps {
  title: string;
  subtitle?: string;
  rightContent?: React.ReactNode;
  leftIcon?: React.ReactNode;
  onClick?: () => void;
  className?: string;
}

export const MobileListItem: React.FC<MobileListItemProps> = ({
  title,
  subtitle,
  rightContent,
  leftIcon,
  onClick,
  className = "",
}) => {
  return (
    <div
      className={`flex items-center space-x-3 p-4 bg-white dark:bg-dark-secondary border border-amspm-light-gray dark:border-dark-border rounded-lg ${
        onClick ? "cursor-pointer hover:bg-amspm-light-gray dark:hover:bg-dark-hover" : ""
      } ${className}`}
      onClick={onClick}
    >
      {leftIcon && (
        <div className="flex-shrink-0 text-amspm-primary dark:text-dark-accent">
          {leftIcon}
        </div>
      )}
      
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium text-amspm-text dark:text-dark-text truncate">
          {title}
        </div>
        {subtitle && (
          <div className="text-xs text-amspm-text-light dark:text-dark-text-light truncate">
            {subtitle}
          </div>
        )}
      </div>
      
      {rightContent && (
        <div className="flex-shrink-0">
          {rightContent}
        </div>
      )}
    </div>
  );
};

// Mobile-specific status badge
interface MobileStatusBadgeProps {
  status: "active" | "inactive" | "pending" | "completed" | "expired";
  size?: "sm" | "md";
}

export const MobileStatusBadge: React.FC<MobileStatusBadgeProps> = ({
  status,
  size = "sm",
}) => {
  const sizeClasses = {
    sm: "px-2 py-1 text-xs",
    md: "px-3 py-1 text-sm",
  };

  const statusClasses = {
    active: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
    inactive: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",
    pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",
    completed: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
    expired: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400",
  };

  return (
    <span
      className={`inline-flex items-center rounded-full font-medium ${sizeClasses[size]} ${statusClasses[status]}`}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
};

// Mobile-specific action button
interface MobileActionButtonProps {
  children: React.ReactNode;
  onClick: () => void;
  variant?: "primary" | "secondary" | "danger";
  size?: "sm" | "md" | "lg";
  fullWidth?: boolean;
  disabled?: boolean;
}

export const MobileActionButton: React.FC<MobileActionButtonProps> = ({
  children,
  onClick,
  variant = "primary",
  size = "md",
  fullWidth = false,
  disabled = false,
}) => {
  const baseClasses = "inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2";
  
  const sizeClasses = {
    sm: "px-3 py-2 text-sm",
    md: "px-4 py-2 text-sm",
    lg: "px-6 py-3 text-base",
  };

  const variantClasses = {
    primary: "bg-amspm-primary hover:bg-amspm-primary-dark text-white focus:ring-amspm-primary dark:bg-dark-accent dark:hover:bg-dark-accent-dark",
    secondary: "bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-white",
    danger: "bg-red-600 hover:bg-red-700 text-white focus:ring-red-500",
  };

  const disabledClasses = "opacity-50 cursor-not-allowed";

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${
        fullWidth ? "w-full" : ""
      } ${disabled ? disabledClasses : ""}`}
    >
      {children}
    </button>
  );
};
