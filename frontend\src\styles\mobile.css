/* Mobile-specific styles for the Customer Management App */

/* Base mobile styles */
@media (max-width: 767px) {
  /* Ensure full viewport usage */
  html, body {
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
  }

  /* Mobile-friendly touch targets */
  button, a, input, select, textarea {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improved text readability on mobile */
  body {
    font-size: 16px;
    line-height: 1.5;
  }

  /* Mobile card spacing */
  .mobile-card {
    margin-bottom: 1rem;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }

  /* Mobile form elements */
  .mobile-input {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
  }

  /* Mobile button styles */
  .mobile-button {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    border-radius: 0.5rem;
    font-weight: 500;
  }

  /* Mobile navigation */
  .mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 50;
    background: white;
    border-top: 1px solid #e5e7eb;
    padding: 0.5rem 0;
  }

  .dark .mobile-nav {
    background: #1f2937;
    border-top-color: #374151;
  }

  /* Mobile sidebar */
  .mobile-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    width: 280px;
    z-index: 40;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }

  .mobile-sidebar.open {
    transform: translateX(0);
  }

  /* Mobile header */
  .mobile-header {
    position: sticky;
    top: 0;
    z-index: 30;
    background: white;
    border-bottom: 1px solid #e5e7eb;
    padding: 0.75rem 1rem;
  }

  .dark .mobile-header {
    background: #1f2937;
    border-bottom-color: #374151;
  }

  /* Mobile content area */
  .mobile-content {
    padding: 1rem;
    padding-bottom: 5rem; /* Space for bottom navigation */
    min-height: calc(100vh - 60px); /* Account for header */
  }

  /* Mobile modal adjustments */
  .mobile-modal {
    margin: 0;
    border-radius: 1rem 1rem 0 0;
    max-height: 90vh;
    overflow-y: auto;
  }

  /* Mobile table alternatives */
  .mobile-table {
    display: none;
  }

  .mobile-list {
    display: block;
  }

  /* Mobile-friendly spacing */
  .mobile-spacing > * + * {
    margin-top: 1rem;
  }

  /* Mobile grid adjustments */
  .mobile-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .mobile-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  /* Mobile typography */
  .mobile-title {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1.3;
  }

  .mobile-subtitle {
    font-size: 1.125rem;
    font-weight: 600;
    line-height: 1.4;
  }

  .mobile-body {
    font-size: 1rem;
    line-height: 1.5;
  }

  .mobile-caption {
    font-size: 0.875rem;
    line-height: 1.4;
  }

  /* Mobile status indicators */
  .mobile-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
  }

  /* Mobile loading states */
  .mobile-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
  }

  /* Mobile error states */
  .mobile-error {
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
  }

  .dark .mobile-error {
    background-color: #7f1d1d;
    border-color: #991b1b;
    color: #fca5a5;
  }

  /* Mobile success states */
  .mobile-success {
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #16a34a;
  }

  .dark .mobile-success {
    background-color: #14532d;
    border-color: #166534;
    color: #86efac;
  }

  /* Mobile animations */
  .mobile-fade-in {
    animation: mobileSlideUp 0.3s ease-out;
  }

  @keyframes mobileSlideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Mobile scroll improvements */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    overflow-y: auto;
  }

  /* Mobile safe areas (for devices with notches) */
  .mobile-safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .mobile-safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Mobile landscape adjustments */
  @media (orientation: landscape) and (max-height: 500px) {
    .mobile-header {
      padding: 0.5rem 1rem;
    }
    
    .mobile-content {
      padding: 0.75rem;
    }
    
    .mobile-nav {
      padding: 0.25rem 0;
    }
  }
}

/* Tablet adjustments */
@media (min-width: 768px) and (max-width: 1023px) {
  .tablet-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .tablet-grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* High DPI display adjustments */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .mobile-icon {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Accessibility improvements for mobile */
@media (prefers-reduced-motion: reduce) {
  .mobile-sidebar {
    transition: none;
  }
  
  .mobile-fade-in {
    animation: none;
  }
}

/* Dark mode mobile adjustments */
.dark .mobile-card {
  background-color: #1f2937;
  border-color: #374151;
}

.dark .mobile-input {
  background-color: #1f2937;
  border-color: #374151;
  color: #f9fafb;
}

.dark .mobile-button {
  background-color: #3b82f6;
  color: white;
}

.dark .mobile-button:hover {
  background-color: #2563eb;
}
