// frontend/src/pages/AdminDashboard.tsx
import React, { useState, useEffect } from "react";
import { useAuth } from "../context/AuthContext";
import { useConfirmation } from "../context/ConfirmationContext";
import { useNavigate } from "react-router-dom";
import { getUpcomingExpirations, getExpiredDocuments } from "../services/documentService";
import { getDashboardMetrics, DashboardMetrics, createEvent } from "../services/eventService";
import { getRecentActivities, Activity } from "../services/activityService";
import { getAllCustomersNoPage } from "../services/customerService";
import { getAllUsers } from "../services/userService";
import { Document } from "../types/document";
import { Customer } from "../types/customer";
import { User } from "../types/user";
import EventModal from "../components/EventModal";
import LoadingSpinner from '../components/LoadingSpinner';
import { toast } from 'react-toastify';
import MetricsCard from "../components/dashboard/MetricsCard";
import ChartWidget from "../components/dashboard/ChartWidget";
import ActivityWidget from "../components/dashboard/ActivityWidget";
import QuickActionsWidget from "../components/dashboard/QuickActionsWidget";
import ExpiryTimeline from "../components/dashboard/ExpiryTimeline";
import { FaUserCog, FaUsers, FaBuilding, FaCalendarAlt, FaExclamationTriangle, FaPlus, FaChartBar,
  FaClipboardCheck, FaClock, FaFileAlt, FaFile, FaExclamation, FaListAlt, FaUserClock } from "react-icons/fa";

const AdminDashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const { showConfirmation } = useConfirmation();
  const navigate = useNavigate();
  const [upcomingDocuments, setUpcomingDocuments] = useState<Document[]>([]);
  const [expiredDocuments, setExpiredDocuments] = useState<Document[]>([]);
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [recentActivities, setRecentActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);

  // New state for EventModal
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [showEventModal, setShowEventModal] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [newEvent, setNewEvent] = useState({
    customer_id: null as number | null,
    event_type: '',
    description: '',
    scheduled_date: '',
    user_ids: [] as number[],
    user_id: null as number | null
  });

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        // Fetch upcoming expirations
        const expirationsResponse = await getUpcomingExpirations();
        setUpcomingDocuments(expirationsResponse.documents);

        // Fetch expired documents
        const expiredResponse = await getExpiredDocuments();
        setExpiredDocuments(expiredResponse.documents);

        // Fetch dashboard metrics
        const metricsResponse = await getDashboardMetrics();
        setMetrics(metricsResponse);

        // Fetch recent activities
        const activitiesResponse = await getRecentActivities(10);
        setRecentActivities(activitiesResponse.activities);

        // Fetch customers and users for EventModal
        const customersResponse = await getAllCustomersNoPage();
        setCustomers(customersResponse.customers);

        const usersResponse = await getAllUsers();
        setUsers(usersResponse.users);
      } catch (err) {
        console.error('Error:', err);
        setError("Failed to fetch dashboard data");
      } finally {
        setLoading(false);
      }
    };
    fetchDashboardData();
  }, []);

  if (loading) {
    return <LoadingSpinner message="Loading dashboard data..." />;
  }

  const handleLogout = async () => {
    showConfirmation({
      title: "Logout Confirmation",
      message: "Are you sure you want to log out?",
      confirmText: "Logout",
      confirmButtonClass: "bg-blue-600 hover:bg-blue-700",
      onConfirm: async () => {
        await logout();
        navigate("/login");
      }
    });
  };

  const handleEventCreated = () => {
    const refreshData = async () => {
      try {
        // Refresh upcoming expirations
        const expirationsResponse = await getUpcomingExpirations();
        setUpcomingDocuments(expirationsResponse?.documents.filter(doc => doc.status === 'active') || []);

        // Refresh expired documents
        const expiredResponse = await getExpiredDocuments();
        setExpiredDocuments(expiredResponse?.documents.filter(doc => doc.status === 'active') || []);

        // Refresh metrics
        const metricsResponse = await getDashboardMetrics();
        setMetrics(metricsResponse);
      } catch (err) {
        setError("Failed to refresh dashboard data");
        console.error(err);
      }
    };
    refreshData();
  };

  const handleCreateEventFromDocument = (document: Document) => {
    // Store the selected document for later reference
    setSelectedDocument(document);

    // Determine event type based on document
    const isQuotation = document?.document_type === 'offerte';
    const eventType = isQuotation ? 'checklist oplevering installatie' : document.document_type;

    // Set default description
    let defaultDescription = '';
    if (isQuotation) {
      defaultDescription = `Installatie voor offerte (document ID: ${document.id})`;
    } else {
      defaultDescription = `Renew document: ${document.document_type} for customer ID: ${document.customer_id}`;
    }

    // Set up the new event
    setNewEvent({
      customer_id: document.customer_id,
      event_type: eventType,
      description: defaultDescription,
      scheduled_date: new Date().toISOString().slice(0, 16),
      user_ids: [],
      user_id: null
    });

    setShowEventModal(true);
  };

  const handleCreateEvent = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const formattedDate = new Date(newEvent.scheduled_date).toISOString();
      await createEvent(
        newEvent.customer_id,
        newEvent.event_type,
        newEvent.description,
        formattedDate,
        newEvent.user_ids.length > 0 ? newEvent.user_ids : null
      );

      // Immediately remove the document from the lists to provide instant feedback
      if (selectedDocument) {
        setUpcomingDocuments(prev => prev.filter(doc => doc.id !== selectedDocument.id));
        setExpiredDocuments(prev => prev.filter(doc => doc.id !== selectedDocument.id));
      }

      setShowEventModal(false);
      setSelectedDocument(null);
      handleEventCreated();
      toast.success('Event created successfully - Document removed from expiry list');
    } catch (err) {
      console.error('Error creating event:', err);
      setError('Failed to create event');
      toast.error('Failed to create event');
      // Don't remove the document from the list if event creation failed
      // The selectedDocument will remain so user can try again
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-8 space-y-3 sm:space-y-0">
        <div className="flex items-center">
          <FaUserCog className="text-amspm-primary mr-2 sm:mr-3 hidden sm:block" size={30} />
          <FaUserCog className="text-amspm-primary mr-2 sm:hidden" size={24} />
          <h1 className="text-2xl sm:text-3xl font-bold text-amspm-text">Admin Dashboard</h1>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-amspm-text mr-2">Welcome, {user?.name || user?.email}</span>
          <button
            onClick={handleLogout}
            className="btn btn-outline text-sm"
          >
            Logout
          </button>
        </div>
      </div>

      {/* Dashboard Metrics */}
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <FaChartBar className="text-amspm-primary mr-2 sm:mr-3 hidden sm:block" size={24} />
          <FaChartBar className="text-amspm-primary mr-2 sm:hidden" size={20} />
          <h2 className="text-xl sm:text-2xl font-semibold text-amspm-text">Dashboard Metrics</h2>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Total Events Card */}
          <MetricsCard
            title="Total Events"
            value={metrics?.events.total || 0}
            icon={<FaCalendarAlt size={20} />}
            subtext={
              <div className="flex justify-between mt-2 text-sm">
                <span className="flex items-center">
                  <FaClock className="text-orange-500 mr-1" size={14} />
                  <span>Pending: {metrics?.events.pending || 0}</span>
                </span>
                <span className="flex items-center">
                  <FaClipboardCheck className="text-green-500 mr-1" size={14} />
                  <span>Completed: {metrics?.events.completed || 0}</span>
                </span>
              </div>
            }
            onClick={() => navigate('/events')}
          />

          {/* Upcoming Events Card */}
          <MetricsCard
            title="Upcoming Events"
            value={metrics?.events.upcoming || 0}
            icon={<FaClock size={20} />}
            color="text-orange-500"
            subtext="Events scheduled in the next 7 days"
            onClick={() => navigate('/calendar')}
          />

          {/* Users Card */}
          <MetricsCard
            title="Total Users"
            value={metrics?.users.total || 0}
            icon={<FaUsers size={20} />}
            subtext="Active system users"
            onClick={() => navigate('/users')}
          />

          {/* Customers Card */}
          <MetricsCard
            title="Total Customers"
            value={metrics?.customers.total || 0}
            icon={<FaBuilding size={20} />}
            subtext="Registered customers"
            onClick={() => navigate('/customers')}
          />
        </div>
      </div>

      {/* Document Metrics */}
      {metrics?.documents && (
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <FaFile className="text-amspm-primary mr-2 sm:mr-3 hidden sm:block" size={24} />
            <FaFile className="text-amspm-primary mr-2 sm:hidden" size={20} />
            <h2 className="text-xl sm:text-2xl font-semibold text-amspm-text">Document Metrics</h2>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Total Active Documents */}
            <MetricsCard
              title="Active Documents"
              value={metrics.documents.total_active || 0}
              icon={<FaFile size={20} />}
              subtext="Total active documents in system"
            />

            {/* Expiring Documents */}
            <MetricsCard
              title="Expiring Soon"
              value={metrics.documents.expiring_soon || 0}
              icon={<FaExclamation size={20} />}
              color="text-orange-500"
              subtext="Documents expiring in next 60 days"
            />

            {/* Expired Documents */}
            <MetricsCard
              title="Expired Documents"
              value={metrics.documents.expired || 0}
              icon={<FaExclamationTriangle size={20} />}
              color="text-red-500"
              subtext="Documents that have already expired"
            />
          </div>

          {/* Document Status Distribution */}
          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Document Status Chart */}
            <ChartWidget
              title="Documents by Status"
              type="doughnut"
              data={{
                labels: ['Critical', 'Warning', 'Good'],
                datasets: [
                  {
                    data: [
                      metrics.documents.by_status.red || 0,
                      metrics.documents.by_status.orange || 0,
                      metrics.documents.by_status.green || 0
                    ],
                    backgroundColor: ['#EF4444', '#F59E0B', '#10B981'],
                    borderColor: ['#EF4444', '#F59E0B', '#10B981'],
                    borderWidth: 1,
                  },
                ],
              }}
              options={{
                plugins: {
                  legend: {
                    position: 'bottom',
                  },
                },
                cutout: '60%',
              }}
              height={250}
            />

            {/* Document Expiry Timeline */}
            <ExpiryTimeline
              documents={upcomingDocuments}
              maxItems={5}
            />
          </div>
        </div>
      )}

      {/* Event Types Distribution and Activity */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
        {/* Event Types Chart */}
        {metrics?.events.by_type && Object.keys(metrics.events.by_type).length > 0 && (
          <ChartWidget
            title="Events by Type"
            type="bar"
            data={{
              labels: Object.keys(metrics.events.by_type),
              datasets: [
                {
                  label: 'Event Count',
                  data: Object.values(metrics.events.by_type),
                  backgroundColor: '#3B82F6',
                  borderColor: '#2563EB',
                  borderWidth: 1,
                },
              ],
            }}
            options={{
              plugins: {
                legend: {
                  display: false,
                },
              },
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    precision: 0
                  }
                }
              }
            }}
            height={300}
          />
        )}

        {/* Recent Activity */}
        <ActivityWidget
          activities={recentActivities}
          maxItems={5}
          onViewAll={() => navigate('/audit')}
        />
      </div>

      {/* Quick Actions Widget */}
      <QuickActionsWidget
        actions={[
          {
            id: 'users',
            title: 'Manage Users',
            icon: <FaUsers size={24} />,
            path: '/users',
            color: 'text-blue-500'
          },
          {
            id: 'customers',
            title: 'Manage Customers',
            icon: <FaBuilding size={24} />,
            path: '/customers',
            color: 'text-green-500'
          },
          {
            id: 'calendar',
            title: 'Calendar View',
            icon: <FaCalendarAlt size={24} />,
            path: '/calendar',
            color: 'text-purple-500'
          },
          {
            id: 'templates',
            title: 'Document Templates',
            icon: <FaFileAlt size={24} />,
            path: '/document-templates',
            color: 'text-orange-500'
          },
          {
            id: 'events',
            title: 'Manage Events',
            icon: <FaClock size={24} />,
            path: '/events',
            color: 'text-red-500'
          },
          {
            id: 'timetracking',
            title: 'Time Tracking',
            icon: <FaUserClock size={24} />,
            path: '/time-tracking-admin',
            color: 'text-teal-500'
          },
          {
            id: 'audit',
            title: 'Audit Logs',
            icon: <FaListAlt size={24} />,
            path: '/audit',
            color: 'text-gray-500'
          }
        ]}
      />

      {/* Upcoming Document Expirations */}
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <FaExclamationTriangle className="text-orange-500 mr-2 sm:mr-3 hidden sm:block" size={24} />
          <FaExclamationTriangle className="text-orange-500 mr-2 sm:hidden" size={20} />
          <h2 className="text-xl sm:text-2xl font-semibold text-amspm-text">Upcoming Document Expirations</h2>
        </div>

        {error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">{error}</div>
        ) : upcomingDocuments.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {upcomingDocuments.slice(0, 6).map((doc) => (
              <div
                key={doc.id}
                onClick={() => handleCreateEventFromDocument(doc)}
                className={`card cursor-pointer hover:shadow-md transition-shadow duration-300 ${doc.expiry_status === "red" ? "border-red-500" : doc.expiry_status === "orange" ? "border-orange-500" : "border-green-500"}`}
              >
                <div className="card-content">
                  <div className="flex items-center mb-2">
                    <FaExclamationTriangle className={`mr-2 ${doc.expiry_status === "red" ? "text-red-500" : doc.expiry_status === "orange" ? "text-orange-500" : "text-green-500"}`} />
                    <span className="font-semibold">{doc.document_type}</span>
                  </div>
                  <p className="text-sm text-gray-600"><span className="font-medium">Customer:</span> {doc.customer_name || `ID: ${doc.customer_id}`}</p>
                  <p className="text-sm text-gray-600"><span className="font-medium">Expiry Date:</span> {doc.expiry_date ? new Date(doc.expiry_date).toLocaleDateString() : "N/A"}</p>
                  <div className="flex justify-between items-center mt-3">
                    <span className={`text-sm font-medium px-2 py-1 rounded-full ${doc.expiry_status === "red" ? "bg-red-100 text-red-700" : doc.expiry_status === "orange" ? "bg-orange-100 text-orange-700" : "bg-green-100 text-green-700"}`}>
                      {doc.expiry_status === "red" ? "Critical" : doc.expiry_status === "orange" ? "Warning" : "Good"}
                    </span>
                    <button
                      className="btn btn-secondary text-xs py-1 px-2 flex items-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCreateEventFromDocument(doc);
                      }}
                    >
                      <FaPlus className="mr-1" size={12} /> Create Event
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-center text-gray-500 py-4">No upcoming expirations.</p>
        )}
      </div>

      {/* Expired Documents */}
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <FaExclamationTriangle className="text-red-500 mr-2 sm:mr-3 hidden sm:block" size={24} />
          <FaExclamationTriangle className="text-red-500 mr-2 sm:hidden" size={20} />
          <h2 className="text-xl sm:text-2xl font-semibold text-amspm-text">Expired Documents</h2>
        </div>

        {error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">{error}</div>
        ) : expiredDocuments.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {expiredDocuments.slice(0, 6).map((doc) => (
              <div
                key={doc.id}
                onClick={() => handleCreateEventFromDocument(doc)}
                className="card cursor-pointer hover:shadow-md transition-shadow duration-300 border-red-500"
              >
                <div className="card-content">
                  <div className="flex items-center mb-2">
                    <FaExclamationTriangle className="mr-2 text-red-500" />
                    <span className="font-semibold">{doc.document_type}</span>
                  </div>
                  <p className="text-sm text-gray-600"><span className="font-medium">Customer:</span> {doc.customer_name || `ID: ${doc.customer_id}`}</p>
                  <p className="text-sm text-gray-600"><span className="font-medium">Expiry Date:</span> {doc.expiry_date ? new Date(doc.expiry_date).toLocaleDateString() : "N/A"}</p>
                  <div className="flex justify-between items-center mt-3">
                    <span className="text-sm font-medium px-2 py-1 rounded-full bg-red-100 text-red-700">
                      Expired
                    </span>
                    <button
                      className="btn btn-secondary text-xs py-1 px-2 flex items-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCreateEventFromDocument(doc);
                      }}
                    >
                      <FaPlus className="mr-1" size={12} /> Create Event
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-center text-gray-500 py-4">No expired documents.</p>
        )}
      </div>
      {showEventModal && (
        <EventModal
          event={newEvent}
          onClose={() => {
            setShowEventModal(false);
            setSelectedDocument(null);
          }}
          onSubmit={handleCreateEvent}
          setEvent={setNewEvent}
          isEditing={false}
          submitting={submitting}
          customers={customers}
          users={users}
        />
      )}
    </div>
  );
};

export default AdminDashboard;
