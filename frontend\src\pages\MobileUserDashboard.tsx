import React, { useState, useEffect } from "react";
import { useAuth } from "../context/AuthContext";
import { getUserEvents, completeEvent } from "../services/eventService";
import { Event } from "../types/Event";
import MobileCard, { MobileListItem, MobileStatusBadge, MobileActionButton } from "../components/MobileCard";
import { FaCalendarAlt, FaClock, FaMapMarkerAlt, FaUser, FaCheck, FaPlus } from "react-icons/fa";
// import { formatDate } from "../utils/dateUtils";

// Temporary formatDate function until utils are available
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('nl-NL', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const MobileUserDashboard: React.FC = () => {
  const { user } = useAuth();
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [completingEvent, setCompletingEvent] = useState<number | null>(null);

  useEffect(() => {
    fetchEvents();
  }, []);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      const response = await getUserEvents();
      setEvents(response.events || []);
    } catch (error) {
      console.error("Error fetching events:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCompleteEvent = async (eventId: number) => {
    try {
      setCompletingEvent(eventId);
      await completeEvent(eventId);
      await fetchEvents(); // Refresh events
    } catch (error) {
      console.error("Error completing event:", error);
    } finally {
      setCompletingEvent(null);
    }
  };

  const getEventStatusColor = (event: Event) => {
    if (event.completed_at) return "completed";
    
    const eventDate = new Date(event.event_date);
    const today = new Date();
    const diffTime = eventDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return "expired";
    if (diffDays <= 1) return "pending";
    return "active";
  };

  const upcomingEvents = events.filter(event => !event.completed_at);
  const completedEvents = events.filter(event => event.completed_at);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amspm-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <MobileCard>
        <div className="text-center">
          <h1 className="text-xl font-bold text-amspm-text dark:text-dark-text mb-2">
            Welcome back, {user?.name || user?.email}!
          </h1>
          <p className="text-sm text-amspm-text-light dark:text-dark-text-light">
            You have {upcomingEvents.length} upcoming events
          </p>
        </div>
      </MobileCard>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 gap-4">
        <MobileCard className="text-center">
          <div className="text-2xl font-bold text-amspm-primary dark:text-dark-accent">
            {upcomingEvents.length}
          </div>
          <div className="text-sm text-amspm-text-light dark:text-dark-text-light">
            Upcoming
          </div>
        </MobileCard>
        <MobileCard className="text-center">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {completedEvents.length}
          </div>
          <div className="text-sm text-amspm-text-light dark:text-dark-text-light">
            Completed
          </div>
        </MobileCard>
      </div>

      {/* Upcoming Events */}
      <div>
        <h2 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-4">
          Upcoming Events
        </h2>
        
        {upcomingEvents.length === 0 ? (
          <MobileCard>
            <div className="text-center py-8">
              <FaCalendarAlt className="text-4xl text-amspm-text-light dark:text-dark-text-light mx-auto mb-4" />
              <p className="text-amspm-text-light dark:text-dark-text-light">
                No upcoming events
              </p>
            </div>
          </MobileCard>
        ) : (
          <div className="space-y-3">
            {upcomingEvents.map((event) => (
              <MobileCard key={event.id} className="border-l-4 border-l-amspm-primary dark:border-l-dark-accent">
                <div className="space-y-3">
                  {/* Event Header */}
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-medium text-amspm-text dark:text-dark-text">
                        {event.document_type}
                      </h3>
                      <p className="text-sm text-amspm-text-light dark:text-dark-text-light">
                        {event.customer_name}
                      </p>
                    </div>
                    <MobileStatusBadge status={getEventStatusColor(event)} />
                  </div>

                  {/* Event Details */}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2 text-sm text-amspm-text-light dark:text-dark-text-light">
                      <FaCalendarAlt className="text-xs" />
                      <span>{formatDate(event.event_date)}</span>
                    </div>
                    
                    {event.event_time && (
                      <div className="flex items-center space-x-2 text-sm text-amspm-text-light dark:text-dark-text-light">
                        <FaClock className="text-xs" />
                        <span>{event.event_time}</span>
                      </div>
                    )}
                    
                    {event.location && (
                      <div className="flex items-center space-x-2 text-sm text-amspm-text-light dark:text-dark-text-light">
                        <FaMapMarkerAlt className="text-xs" />
                        <span>{event.location}</span>
                      </div>
                    )}
                  </div>

                  {/* Action Button */}
                  <MobileActionButton
                    onClick={() => handleCompleteEvent(event.id)}
                    disabled={completingEvent === event.id}
                    fullWidth
                    size="sm"
                  >
                    {completingEvent === event.id ? (
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>Completing...</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <FaCheck />
                        <span>Mark Complete</span>
                      </div>
                    )}
                  </MobileActionButton>
                </div>
              </MobileCard>
            ))}
          </div>
        )}
      </div>

      {/* Recent Completed Events */}
      {completedEvents.length > 0 && (
        <div>
          <h2 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-4">
            Recently Completed
          </h2>
          
          <div className="space-y-3">
            {completedEvents.slice(0, 3).map((event) => (
              <MobileListItem
                key={event.id}
                title={event.document_type}
                subtitle={`${event.customer_name} • Completed ${formatDate(event.completed_at!)}`}
                leftIcon={<FaCheck className="text-green-600" />}
                rightContent={<MobileStatusBadge status="completed" />}
              />
            ))}
          </div>
          
          {completedEvents.length > 3 && (
            <div className="mt-4 text-center">
              <button className="text-amspm-primary dark:text-dark-accent text-sm font-medium">
                View all completed events
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MobileUserDashboard;
