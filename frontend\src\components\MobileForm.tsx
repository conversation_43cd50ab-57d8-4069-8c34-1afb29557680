import React from "react";

interface MobileFormProps {
  children: React.ReactNode;
  onSubmit: (e: React.FormEvent) => void;
  className?: string;
}

export const MobileForm: React.FC<MobileFormProps> = ({
  children,
  onSubmit,
  className = "",
}) => {
  return (
    <form
      onSubmit={onSubmit}
      className={`space-y-4 ${className}`}
    >
      {children}
    </form>
  );
};

interface MobileInputProps {
  label: string;
  type?: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
  rows?: number;
}

export const MobileInput: React.FC<MobileInputProps> = ({
  label,
  type = "text",
  value,
  onChange,
  placeholder,
  required = false,
  error,
  disabled = false,
  rows,
}) => {
  const inputClasses = `w-full px-4 py-3 text-base border rounded-lg focus:ring-2 focus:ring-amspm-primary focus:border-transparent transition-colors ${
    error
      ? "border-red-500 bg-red-50 dark:bg-red-900/20"
      : "border-amspm-light-gray dark:border-dark-border bg-white dark:bg-dark-secondary"
  } ${
    disabled
      ? "opacity-50 cursor-not-allowed"
      : "text-amspm-text dark:text-dark-text"
  }`;

  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-amspm-text dark:text-dark-text">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      
      {type === "textarea" ? (
        <textarea
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          rows={rows || 4}
          className={inputClasses}
        />
      ) : (
        <input
          type={type}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          className={inputClasses}
        />
      )}
      
      {error && (
        <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
      )}
    </div>
  );
};

interface MobileSelectProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  options: { value: string; label: string }[];
  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
}

export const MobileSelect: React.FC<MobileSelectProps> = ({
  label,
  value,
  onChange,
  options,
  placeholder,
  required = false,
  error,
  disabled = false,
}) => {
  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-amspm-text dark:text-dark-text">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        required={required}
        disabled={disabled}
        className={`w-full px-4 py-3 text-base border rounded-lg focus:ring-2 focus:ring-amspm-primary focus:border-transparent transition-colors ${
          error
            ? "border-red-500 bg-red-50 dark:bg-red-900/20"
            : "border-amspm-light-gray dark:border-dark-border bg-white dark:bg-dark-secondary"
        } ${
          disabled
            ? "opacity-50 cursor-not-allowed"
            : "text-amspm-text dark:text-dark-text"
        }`}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      
      {error && (
        <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
      )}
    </div>
  );
};

interface MobileCheckboxProps {
  label: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
}

export const MobileCheckbox: React.FC<MobileCheckboxProps> = ({
  label,
  checked,
  onChange,
  disabled = false,
}) => {
  return (
    <label className="flex items-center space-x-3 cursor-pointer">
      <input
        type="checkbox"
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
        disabled={disabled}
        className="w-5 h-5 text-amspm-primary border-amspm-light-gray rounded focus:ring-amspm-primary focus:ring-2"
      />
      <span className={`text-base ${
        disabled 
          ? "text-amspm-text-light dark:text-dark-text-light" 
          : "text-amspm-text dark:text-dark-text"
      }`}>
        {label}
      </span>
    </label>
  );
};

interface MobileButtonGroupProps {
  children: React.ReactNode;
  className?: string;
}

export const MobileButtonGroup: React.FC<MobileButtonGroupProps> = ({
  children,
  className = "",
}) => {
  return (
    <div className={`flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-3 ${className}`}>
      {children}
    </div>
  );
};

interface MobileModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

export const MobileModal: React.FC<MobileModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Backdrop */}
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={onClose}
        />

        {/* Modal */}
        <div className="inline-block w-full max-w-lg transform overflow-hidden rounded-t-lg bg-white dark:bg-dark-secondary text-left align-bottom shadow-xl transition-all sm:my-8 sm:align-middle sm:rounded-lg">
          {/* Header */}
          <div className="border-b border-amspm-light-gray dark:border-dark-border px-6 py-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-amspm-text dark:text-dark-text">
                {title}
              </h3>
              <button
                onClick={onClose}
                className="text-amspm-text-light dark:text-dark-text-light hover:text-amspm-text dark:hover:text-dark-text"
              >
                ✕
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="px-6 py-4">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};
