import React, { useState, useEffect } from "react";
import { useAuth } from "../context/AuthContext";
import Layout from "./Layout";
import MobileLayout from "./MobileLayout";

interface ResponsiveLayoutProps {
  children: React.ReactNode;
}

const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({ children }) => {
  const [isMobile, setIsMobile] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Check initial screen size
    checkScreenSize();

    // Add event listener for window resize
    window.addEventListener("resize", checkScreenSize);

    // Cleanup event listener
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  // Don't render anything if user is not authenticated
  if (!user) {
    return <>{children}</>;
  }

  // Use mobile layout for small screens, desktop layout for larger screens
  if (isMobile) {
    return <MobileLayout>{children}</MobileLayout>;
  } else {
    return <Layout>{children}</Layout>;
  }
};

export default ResponsiveLayout;
