import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import { useTheme } from "../context/ThemeContext";
import MobileNavigation from "./MobileNavigation";
import MobileHeader from "./MobileHeader";
import { FaBars, FaTimes } from "react-icons/fa";

interface MobileLayoutProps {
  children: React.ReactNode;
}

const MobileLayout: React.FC<MobileLayoutProps> = ({ children }) => {
  const { theme, toggleTheme } = useTheme();
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      if (!mobile) {
        setSidebarOpen(false);
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Close sidebar when route changes on mobile
  useEffect(() => {
    if (isMobile) {
      setSidebarOpen(false);
    }
  }, [location.pathname, isMobile]);

  const handleLogout = async () => {
    try {
      await logout();
      navigate("/login");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  if (!user) return null;

  return (
    <div className="flex flex-col h-screen bg-amspm-background dark:bg-dark-primary overflow-hidden">
      {/* Mobile Header */}
      <MobileHeader 
        user={user}
        onToggleSidebar={toggleSidebar}
        onToggleTheme={toggleTheme}
        theme={theme}
      />

      <div className="flex flex-1 overflow-hidden">
        {/* Mobile Sidebar Overlay */}
        {sidebarOpen && (
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Mobile Navigation */}
        <MobileNavigation
          user={user}
          isOpen={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          onLogout={handleLogout}
        />

        {/* Main Content */}
        <main className="flex-1 overflow-auto">
          <div className="p-4 pb-20"> {/* Extra bottom padding for mobile navigation */}
            {children}
          </div>
        </main>
      </div>

      {/* Bottom Navigation for Mobile */}
      {isMobile && (
        <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-dark-secondary border-t border-amspm-light-gray dark:border-dark-border z-30">
          <div className="flex justify-around items-center py-2">
            {user.role === "administrator" ? (
              <>
                <button
                  onClick={() => navigate("/dashboard")}
                  className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
                    location.pathname === "/dashboard" 
                      ? "text-amspm-primary dark:text-dark-accent" 
                      : "text-amspm-text-light dark:text-dark-text-light"
                  }`}
                >
                  <div className="text-lg mb-1">🏠</div>
                  <span className="text-xs">Dashboard</span>
                </button>
                <button
                  onClick={() => navigate("/customers")}
                  className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
                    location.pathname === "/customers" 
                      ? "text-amspm-primary dark:text-dark-accent" 
                      : "text-amspm-text-light dark:text-dark-text-light"
                  }`}
                >
                  <div className="text-lg mb-1">👥</div>
                  <span className="text-xs">Customers</span>
                </button>
                <button
                  onClick={() => navigate("/events")}
                  className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
                    location.pathname === "/events" 
                      ? "text-amspm-primary dark:text-dark-accent" 
                      : "text-amspm-text-light dark:text-dark-text-light"
                  }`}
                >
                  <div className="text-lg mb-1">📅</div>
                  <span className="text-xs">Events</span>
                </button>
                <button
                  onClick={toggleSidebar}
                  className="flex flex-col items-center p-2 rounded-lg transition-colors text-amspm-text-light dark:text-dark-text-light"
                >
                  <div className="text-lg mb-1">☰</div>
                  <span className="text-xs">More</span>
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={() => navigate("/user-dashboard")}
                  className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
                    location.pathname === "/user-dashboard" 
                      ? "text-amspm-primary dark:text-dark-accent" 
                      : "text-amspm-text-light dark:text-dark-text-light"
                  }`}
                >
                  <div className="text-lg mb-1">🏠</div>
                  <span className="text-xs">Dashboard</span>
                </button>
                <button
                  onClick={() => navigate("/calendar")}
                  className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
                    location.pathname === "/calendar" 
                      ? "text-amspm-primary dark:text-dark-accent" 
                      : "text-amspm-text-light dark:text-dark-text-light"
                  }`}
                >
                  <div className="text-lg mb-1">📅</div>
                  <span className="text-xs">Calendar</span>
                </button>
                <button
                  onClick={() => navigate("/time-tracking")}
                  className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
                    location.pathname === "/time-tracking" 
                      ? "text-amspm-primary dark:text-dark-accent" 
                      : "text-amspm-text-light dark:text-dark-text-light"
                  }`}
                >
                  <div className="text-lg mb-1">⏰</div>
                  <span className="text-xs">Time</span>
                </button>
                <button
                  onClick={toggleSidebar}
                  className="flex flex-col items-center p-2 rounded-lg transition-colors text-amspm-text-light dark:text-dark-text-light"
                >
                  <div className="text-lg mb-1">☰</div>
                  <span className="text-xs">More</span>
                </button>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default MobileLayout;
