// frontend/src/App.tsx
import React, { useState, useEffect } from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider, useAuth } from "./context/AuthContext";
import { ConfirmationProvider } from "./context/ConfirmationContext";
import { ThemeProvider, useTheme } from "./context/ThemeContext";
import Layout from "./components/Layout";
import ResponsiveLayout from "./components/ResponsiveLayout";
import ResponsiveDashboard from "./components/ResponsiveDashboard";
import Login from "./pages/Login";
import AdminDashboard from "./pages/AdminDashboard";
import UserDashboard from "./pages/UserDashboard";
import Users from "./pages/Users";

import Customers from "./pages/Customers";
import ResponsiveCustomers from "./components/ResponsiveCustomers";
import CustomerPage from "./pages/CustomerPage";
import CustomerEditPage from "./pages/CustomerEditPage";
import CustomerDocuments from "./pages/CustomerDocuments";
import UserCustomerDocuments from "./pages/UserCustomerDocuments";
import Events from "./pages/Events";
import Calendar from "./pages/Calendar";
import DocumentTemplatesPage from "./pages/DocumentTemplatesPage";
import ProductsPage from "./pages/ProductsPage";
import QuotationsPage from "./pages/QuotationsPage";
import TimeTracking from "./pages/TimeTracking";
import TimeTrackingAdmin from "./pages/TimeTrackingAdmin";
import TimeTrackingUserDetail from "./pages/TimeTrackingUserDetail";
import TimeTrackingPending from "./pages/TimeTrackingPending";
import TimeTrackingReports from "./pages/TimeTrackingReports";
// SessionsPage removed
import AuditLogs from "./pages/AuditLogs";
import ProfilePage from "./pages/ProfilePage";
import LoadingSpinner from './components/LoadingSpinner';
import ConfirmationDialog from "./components/ConfirmationDialog";

const RedirectMessage: React.FC<{ message: string }> = ({ message }) => (
  <div className="fixed inset-0 bg-white dark:bg-dark-primary flex items-center justify-center">
    <div className="card max-w-md w-full text-center p-8">
      <LoadingSpinner fullScreen={false} />
      <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text mt-6">
        {message}
      </h2>
    </div>
  </div>
);

const ProtectedRoute: React.FC<{
  children: React.ReactNode | ((props: { user: any }) => React.ReactNode);
  role?: string | string[];
}> = ({ children, role }) => {
  const { user, loading } = useAuth();
  const [showMessage, setShowMessage] = useState(true);

  useEffect(() => {
    if (!user || (role && !hasAccess(user, role))) {
      const timer = setTimeout(() => {
        setShowMessage(false);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [user, role]);

  const hasAccess = (user: any, role: string | string[]) => {
    return typeof role === "string"
      ? user.role === role
      : role.includes(user.role);
  };

  if (loading) {
    return <LoadingSpinner message="Loading application..." />;
  }

  if (!user) {
    return showMessage
      ? <RedirectMessage message="Please login to continue, redirecting..." />
      : <Navigate to="/login" />;
  }

  // If user doesn't have the required role, redirect to user dashboard
  if (role && !hasAccess(user, role)) {
    return <Navigate to="/user-dashboard" />;
  }

  return <>{typeof children === "function" ? children({ user }) : children}</>;
};

const AppLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { theme } = useTheme();
  return (
    <div className={`app ${theme}`}>
      <ResponsiveLayout>
        {children}
      </ResponsiveLayout>
    </div>
  );
};

const App: React.FC = () => {
  return (
    <AuthProvider>
      <ThemeProvider>
        <ConfirmationProvider>
          <Router>
            <ConfirmationDialog />
            <Routes>
          {/* Public route: Login */}
          <Route path="/login" element={<Login />} />

          {/* Root route redirect */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                {({ user }) => (
                  <Navigate to={user.role === "administrator" ? "/dashboard" : "/user-dashboard"} replace />
                )}
              </ProtectedRoute>
            }
          />

          {/* Admin-only routes */}
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute role="administrator">
                <AppLayout>
                  <ResponsiveDashboard />
                </AppLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/users"
            element={
              <ProtectedRoute role="administrator">
                <AppLayout>
                  <Users />
                </AppLayout>
              </ProtectedRoute>
            }
          />



          {/* Sessions page removed */}

          <Route
            path="/audit"
            element={
              <ProtectedRoute role="administrator">
                <AppLayout>
                  <AuditLogs />
                </AppLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/document-templates"
            element={
              <ProtectedRoute role="administrator">
                <AppLayout>
                  <DocumentTemplatesPage />
                </AppLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/products/*"
            element={
              <ProtectedRoute role={["administrator", "verkoper"]}>
                <AppLayout>
                  <ProductsPage />
                </AppLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/quotations/*"
            element={
              <ProtectedRoute role={["administrator", "verkoper"]}>
                <AppLayout>
                  <QuotationsPage />
                </AppLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/customers"
            element={
              <ProtectedRoute role="administrator">
                <AppLayout>
                  <ResponsiveCustomers />
                </AppLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/customers/:customerId"
            element={
              <ProtectedRoute role="administrator">
                <AppLayout>
                  <CustomerPage />
                </AppLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/customers/:customerId/edit"
            element={
              <ProtectedRoute role="administrator">
                <AppLayout>
                  <CustomerEditPage />
                </AppLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/customers/:customerId/documents"
            element={
              <ProtectedRoute role="administrator">
                <AppLayout>
                  <CustomerDocuments />
                </AppLayout>
              </ProtectedRoute>
            }
          />



          <Route
            path="/events"
            element={
              <ProtectedRoute role="administrator">
                <AppLayout>
                  <Events />
                </AppLayout>
              </ProtectedRoute>
            }
          />


          {/* User routes (accessible by all authenticated users with specified roles) */}
          <Route
            path="/user-dashboard"
            element={
              <ProtectedRoute role={["monteur", "verkoper", "administrator"]}>
                <AppLayout>
                  <ResponsiveDashboard />
                </AppLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/calendar"
            element={
              <ProtectedRoute role={["monteur", "verkoper", "administrator"]}>
                <AppLayout>
                  <Calendar />
                </AppLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/time-tracking"
            element={
              <ProtectedRoute role={["monteur", "verkoper", "administrator"]}>
                <AppLayout>
                  <TimeTracking />
                </AppLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/time-tracking-admin"
            element={
              <ProtectedRoute role="administrator">
                <AppLayout>
                  <TimeTrackingAdmin />
                </AppLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/time-tracking-admin/user/:userId"
            element={
              <ProtectedRoute role="administrator">
                <AppLayout>
                  <TimeTrackingUserDetail />
                </AppLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/time-tracking-admin/pending"
            element={
              <ProtectedRoute role="administrator">
                <AppLayout>
                  <TimeTrackingPending />
                </AppLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/time-tracking-admin/reports"
            element={
              <ProtectedRoute role="administrator">
                <AppLayout>
                  <TimeTrackingReports />
                </AppLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/customer/:customerId/documents"
            element={
              <ProtectedRoute role={["monteur", "verkoper", "administrator"]}>
                <AppLayout>
                  <UserCustomerDocuments />
                </AppLayout>
              </ProtectedRoute>
            }
          />

          {/* Profile page - accessible to all authenticated users */}
          <Route
            path="/profile"
            element={
              <ProtectedRoute role={["monteur", "verkoper", "administrator"]}>
                <AppLayout>
                  <ProfilePage />
                </AppLayout>
              </ProtectedRoute>
            }
          />

          {/* Catch all unknown routes */}
          <Route path="*" element={<Navigate to="/login" />} />
          </Routes>
        </Router>
      </ConfirmationProvider>
      </ThemeProvider>
    </AuthProvider>
  );
};

export default App;