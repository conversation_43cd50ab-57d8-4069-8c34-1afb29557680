import React, { useState, useEffect } from "react";
import Customers from "../pages/Customers";
import MobileCustomers from "../pages/MobileCustomers";

const ResponsiveCustomers: React.FC = () => {
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return isMobile ? <MobileCustomers /> : <Customers />;
};

export default ResponsiveCustomers;
