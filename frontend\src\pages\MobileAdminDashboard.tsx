import React, { useState, useEffect } from "react";
import { useAuth } from "../context/AuthContext";
import { getUpcomingExpirations, getExpiredDocuments } from "../services/documentService";
import { getUpcomingEvents } from "../services/eventService";
import { Document } from "../types/Document";
import { Event } from "../types/Event";
import MobileCard, { MobileListItem, MobileStatusBadge, MobileActionButton } from "../components/MobileCard";
import { FaCalendarAlt, FaClock, FaExclamationTriangle, FaFileAlt, FaPlus, FaUsers, FaChartBar } from "react-icons/fa";

// Temporary formatDate function
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('nl-NL', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const MobileAdminDashboard: React.FC = () => {
  const { user } = useAuth();
  const [upcomingDocuments, setUpcomingDocuments] = useState<Document[]>([]);
  const [expiredDocuments, setExpiredDocuments] = useState<Document[]>([]);
  const [upcomingEvents, setUpcomingEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const [expirationsResponse, expiredResponse, eventsResponse] = await Promise.all([
        getUpcomingExpirations(),
        getExpiredDocuments(),
        getUpcomingEvents()
      ]);

      setUpcomingDocuments(expirationsResponse?.documents || []);
      setExpiredDocuments(expiredResponse?.documents || []);
      setUpcomingEvents(eventsResponse?.events || []);
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
    } finally {
      setLoading(false);
    }
  };

  const getExpiryStatusColor = (document: Document) => {
    if (!document.expiry_date) return "active";
    
    const expiryDate = new Date(document.expiry_date);
    const today = new Date();
    const diffTime = expiryDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return "expired";
    if (diffDays <= 30) return "pending";
    return "active";
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amspm-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <MobileCard>
        <div className="text-center">
          <h1 className="text-xl font-bold text-amspm-text dark:text-dark-text mb-2">
            Admin Dashboard
          </h1>
          <p className="text-sm text-amspm-text-light dark:text-dark-text-light">
            Welcome back, {user?.name || user?.email}
          </p>
        </div>
      </MobileCard>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 gap-4">
        <MobileCard className="text-center">
          <div className="text-2xl font-bold text-red-600 dark:text-red-400">
            {expiredDocuments.length}
          </div>
          <div className="text-sm text-amspm-text-light dark:text-dark-text-light">
            Expired
          </div>
        </MobileCard>
        <MobileCard className="text-center">
          <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
            {upcomingDocuments.length}
          </div>
          <div className="text-sm text-amspm-text-light dark:text-dark-text-light">
            Expiring Soon
          </div>
        </MobileCard>
      </div>

      {/* Quick Actions */}
      <MobileCard>
        <h2 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-4">
          Quick Actions
        </h2>
        <div className="grid grid-cols-2 gap-3">
          <MobileActionButton
            onClick={() => window.location.href = '/customers'}
            variant="secondary"
            size="sm"
          >
            <div className="flex flex-col items-center space-y-1">
              <FaUsers />
              <span className="text-xs">Customers</span>
            </div>
          </MobileActionButton>
          <MobileActionButton
            onClick={() => window.location.href = '/events'}
            variant="secondary"
            size="sm"
          >
            <div className="flex flex-col items-center space-y-1">
              <FaCalendarAlt />
              <span className="text-xs">Events</span>
            </div>
          </MobileActionButton>
          <MobileActionButton
            onClick={() => window.location.href = '/documents'}
            variant="secondary"
            size="sm"
          >
            <div className="flex flex-col items-center space-y-1">
              <FaFileAlt />
              <span className="text-xs">Documents</span>
            </div>
          </MobileActionButton>
          <MobileActionButton
            onClick={() => window.location.href = '/quotations'}
            variant="secondary"
            size="sm"
          >
            <div className="flex flex-col items-center space-y-1">
              <FaChartBar />
              <span className="text-xs">Quotations</span>
            </div>
          </MobileActionButton>
        </div>
      </MobileCard>

      {/* Expired Documents - High Priority */}
      {expiredDocuments.length > 0 && (
        <div>
          <div className="flex items-center space-x-2 mb-4">
            <FaExclamationTriangle className="text-red-600" />
            <h2 className="text-lg font-semibold text-amspm-text dark:text-dark-text">
              Expired Documents
            </h2>
          </div>
          
          <div className="space-y-3">
            {expiredDocuments.slice(0, 5).map((document) => (
              <MobileCard key={document.id} className="border-l-4 border-l-red-500">
                <div className="space-y-2">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-medium text-amspm-text dark:text-dark-text">
                        {document.document_type}
                      </h3>
                      <p className="text-sm text-amspm-text-light dark:text-dark-text-light">
                        {document.customer_name}
                      </p>
                    </div>
                    <MobileStatusBadge status="expired" />
                  </div>
                  
                  <div className="text-sm text-red-600 dark:text-red-400">
                    Expired: {formatDate(document.expiry_date!)}
                  </div>
                  
                  <MobileActionButton
                    onClick={() => {/* Handle create event */}}
                    size="sm"
                    fullWidth
                  >
                    <div className="flex items-center space-x-2">
                      <FaPlus />
                      <span>Create Event</span>
                    </div>
                  </MobileActionButton>
                </div>
              </MobileCard>
            ))}
          </div>
          
          {expiredDocuments.length > 5 && (
            <div className="mt-4 text-center">
              <button className="text-amspm-primary dark:text-dark-accent text-sm font-medium">
                View all {expiredDocuments.length} expired documents
              </button>
            </div>
          )}
        </div>
      )}

      {/* Upcoming Expirations */}
      {upcomingDocuments.length > 0 && (
        <div>
          <h2 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-4">
            Upcoming Expirations
          </h2>
          
          <div className="space-y-3">
            {upcomingDocuments.slice(0, 5).map((document) => (
              <MobileListItem
                key={document.id}
                title={document.document_type}
                subtitle={`${document.customer_name} • Expires ${formatDate(document.expiry_date!)}`}
                leftIcon={<FaClock className="text-orange-600" />}
                rightContent={<MobileStatusBadge status={getExpiryStatusColor(document)} />}
                onClick={() => {/* Handle document click */}}
              />
            ))}
          </div>
          
          {upcomingDocuments.length > 5 && (
            <div className="mt-4 text-center">
              <button className="text-amspm-primary dark:text-dark-accent text-sm font-medium">
                View all {upcomingDocuments.length} upcoming expirations
              </button>
            </div>
          )}
        </div>
      )}

      {/* Upcoming Events */}
      {upcomingEvents.length > 0 && (
        <div>
          <h2 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-4">
            Upcoming Events
          </h2>
          
          <div className="space-y-3">
            {upcomingEvents.slice(0, 3).map((event) => (
              <MobileListItem
                key={event.id}
                title={event.document_type}
                subtitle={`${event.customer_name} • ${formatDate(event.event_date)}`}
                leftIcon={<FaCalendarAlt className="text-blue-600" />}
                rightContent={<MobileStatusBadge status="active" />}
                onClick={() => {/* Handle event click */}}
              />
            ))}
          </div>
          
          {upcomingEvents.length > 3 && (
            <div className="mt-4 text-center">
              <button className="text-amspm-primary dark:text-dark-accent text-sm font-medium">
                View all events
              </button>
            </div>
          )}
        </div>
      )}

      {/* Empty State */}
      {expiredDocuments.length === 0 && upcomingDocuments.length === 0 && upcomingEvents.length === 0 && (
        <MobileCard>
          <div className="text-center py-8">
            <FaChartBar className="text-4xl text-amspm-text-light dark:text-dark-text-light mx-auto mb-4" />
            <h3 className="text-lg font-medium text-amspm-text dark:text-dark-text mb-2">
              All caught up!
            </h3>
            <p className="text-amspm-text-light dark:text-dark-text-light">
              No urgent items require your attention right now.
            </p>
          </div>
        </MobileCard>
      )}
    </div>
  );
};

export default MobileAdminDashboard;
